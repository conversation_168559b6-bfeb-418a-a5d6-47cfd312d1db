# import os
# import django

# os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
# django.setup()
# from django.utils import timezone

# from Project.models import *
# import base64
# import datetime
# import websocket
# import json
# import threading
# from flask import Flask, request
# from flask_socketio import SocketIO, emit, join_room
# from flask_cors import CORS
# from flask import request

# app = Flask(__name__)
# app.config['SECRET_KEY'] = 'your_secret_key'
# app.config['UPLOAD_FOLDER'] = os.path.join('media', 'prepare_chat_files')

# CORS(app, resources={r"/*": {"origins": "*"}})
# socketio = SocketIO(app, cors_allowed_origins=["https://firecamp.dev","http://localhost:3000","http://localhost:30044","https://room8.flexioninfotech.com"], max_http_buffer_size=1024*1024*400, ping_timeout=120, ping_interval=25)

# # Polygon.io configuration - Use delayed feed for free tier
# POLYGON_API_KEY = "********************************"
# POLYGON_WS_URL = "wss://delayed.polygon.io/stocks"  # Use delayed feed


# # @socketio.on('delete_message')
# # def delete_message(data):
# #     auth_token = data.get('Authorization')
# #     message_id = data.get('message_id')
# #     if not auth_token:
# #         emit('delete_message', {'message': 'JWT Token Required'})
# #         return
# #     user_id = decode_token(f'Bearer {auth_token}')
# #     if not user_id:
# #         emit('delete_message', {'message': 'Invalid JWT Token'})
# #         return
# #     try:
# #         message = ChatMessage.objects.get(pk=message_id)
# #         to_user_id = message.to_user_id
# #         room = str(to_user_id)
# #         message.delete()
# #         socketio.emit('delete_message', {
# #             'status': True,
# #             'message': 'Message deleted successfully',
# #             'message_id': message_id,
# #             'to_user_id': to_user_id
# #         }, to=room)
# #         emit('delete_message', {
# #             'status': True,
# #             'message': 'Message deleted successfully',
# #             'message_id': message_id,
# #             'to_user_id': to_user_id
# #         })
# #     except ChatMessage.DoesNotExist:
# #         emit('delete_message', {'status': False,'message': 'Message not found'})

# # @socketio.on('is_typing')
# # def is_typing(data):
# #     auth_token = data.get('Authorization')
# #     to_room = data.get('to')
# #     is_typing = data.get('is_typing')
# #     if not auth_token:
# #         emit('is_typing', {'message': 'JWT Token Required'})
# #         return
# #     user_id = decode_token(f'Bearer {auth_token}')
# #     if not user_id:
# #         emit('is_typing', {'message': 'Invalid JWT Token'})
# #         return
# #     room = str(to_room)
# #     if is_typing == '1':
# #         socketio.emit('is_typing', {'is_typing': True , 'to':to_room ,'user_id':user_id}, to=room)
# #         socketio.emit('is_typing_list', {'is_typing': True , 'to':to_room ,'user_id':user_id}, to=room)
# #     else:
# #         socketio.emit('is_typing', {'is_typing': False, 'to':to_room ,'user_id':user_id}, to=room)
# #         socketio.emit('is_typing_list', {'is_typing': False, 'to':to_room ,'user_id':user_id}, to=room)

# # @socketio.on('message_read')
# # def is_typing(data):
# #     auth_token = data.get('Authorization')
# #     to_room = data.get('to')
# #     message_id = data.get('message_id')
# #     if not auth_token:
# #         emit('message_read', {'message': 'JWT Token Required'})
# #         return
# #     user_id = decode_token(f'Bearer {auth_token}')
# #     if not user_id:
# #         emit('message_read', {'message': 'Invalid JWT Token'})
# #         return
# #     if not message_id:
# #         emit('message_read', {'message': 'message_id field is required'})
# #         return

# #     room = str(to_room)

# #     try:
# #         message = ChatMessage.objects.get(pk=message_id)
# #         message.is_read = True
# #         message.read_time = datetime.datetime.now()
# #         message.save()
# #         socketio.emit('message_read', {
# #                       'id': message.pk, 'is_read': True, 'read_time': message.read_time}, to=room)

# #     except ChatMessage.DoesNotExist:
# #         emit('message_read', {'message': 'Chat message not found'})

# if __name__ == '__main__':
#     socketio.run(app, allow_unsafe_werkzeug=True, debug=True, port=30044)








# import websocket
# import json
# import threading
# import time

# # Polygon.io API Configuration
# POLYGON_API_KEY = "********************************"
# POLYGON_WS_URL = "wss://delayed.polygon.io/stocks"  
# POLYGON_WS_URL = "wss://socket.polygon.io/stocks"  

# # List of stock symbols to subscribe (comma separated)
# SUBSCRIPTION_SYMBOLS = "AM.AAPL,AM.MSFT"

# def on_message(ws, message):
#     data = json.loads(message)
#     print("\n🔔 Received Message:")
#     print(json.dumps(data, indent=2))

# def on_error(ws, error):
#     print("❌ Error:", error)

# def on_close(ws, close_status_code, close_msg):
#     print("🔒 Connection closed")

# def on_open(ws):
#     def run():
#         print("✅ Connected to Polygon WebSocket")
        
#         # Step 1: Authenticate
#         auth_data = {
#             "action": "auth",
#             "params": POLYGON_API_KEY
#         }
#         ws.send(json.dumps(auth_data))
#         print("🔐 Sent authentication")

#         time.sleep(1)

#         # Step 2: Subscribe to aggregate data
#         sub_data = {
#             "action": "subscribe",
#             "params": SUBSCRIPTION_SYMBOLS
#         }
#         ws.send(json.dumps(sub_data))
#         print(f"📡 Subscribed to: {SUBSCRIPTION_SYMBOLS}")

#     thread = threading.Thread(target=run)
#     thread.start()

# if __name__ == "__main__":
#     print("📶 Connecting to Polygon WebSocket...")
#     ws = websocket.WebSocketApp(
#         POLYGON_WS_URL,
#         on_open=on_open,
#         on_message=on_message,
#         on_error=on_error,
#         on_close=on_close
#     )
#     ws.run_forever()


# # import requests

# # api_key = "********************************"
# # url = f"https://api.polygon.io/v2/aggs/ticker/AAPL/prev?adjusted=true&apiKey={api_key}"

# # response = requests.get(url)
# # data = response.json()

# # print(data)

# import requests

# api_key = "********************************"
# url = f"https://api.polygon.io/v3/reference/dividends?apiKey={api_key}"

# response = requests.get(url)
# data = response.json()

# print(data)













# import websocket
# import json
# import time

# # Polygon.io API Configuration
# POLYGON_API_KEY = "********************************"
# POLYGON_WS_URL = "wss://delayed.polygon.io/stocks"
# # POLYGON_WS_URL = "wss://socket.polygon.io/stocks"

# class PolygonWebSocketClient:
#     def __init__(self, api_key):
#         self.api_key = api_key
#         self.ws = None
        
#     def on_message(self, ws, message):
#         try:
#             data = json.loads(message)
#             print(f"\n🔔 Raw Message: {json.dumps(data, indent=2)}")
            
#             # Check all message types
#             for msg in data:
#                 print(f"Message type: {msg.get('ev')}")
#                 if msg.get('ev') == 'status':
#                     print(f"Status: {msg.get('message')}")
#                 elif msg.get('ev') in ['A', 'AM']:
#                     print(f"Stock data: {msg}")
                    
#         except Exception as e:
#             print(f"❌ Error: {e}")
#             print(f"Raw message: {message}")

#     def on_error(self, ws, error):
#         print(f"❌ Error: {error}")

#     def on_close(self, ws, close_status_code, close_msg):
#         print(f"🔒 Connection closed: {close_status_code} - {close_msg}")

#     def on_open(self, ws):
#         print("✅ Connected to Polygon WebSocket")
        
#         # Step 1: Authenticate
#         auth_data = {"action": "auth", "params": self.api_key}
#         ws.send(json.dumps(auth_data))
#         print("🔐 Authentication sent")
        
#         time.sleep(2)
        
#         # Step 2: Subscribe to symbols
#         subscriptions = ["AM.AAPL", "AM.MSFT"]
        
#         for sub in subscriptions:
#             sub_data = {"action": "subscribe", "params": 'AM.AAPL'}
#             ws.send(json.dumps(sub_data))
#             print(f"📡 Subscribed to: {sub}")
#             time.sleep(1)

#     def start(self):
#         print("📶 Connecting to Polygon WebSocket...")
#         self.ws = websocket.WebSocketApp(
#             POLYGON_WS_URL,
#             on_open=self.on_open,
#             on_message=self.on_message,
#             # on_error=self.on_error,
#             # on_close=self.on_close
#         )
#         self.ws.run_forever()

# if __name__ == "__main__":
#     client = PolygonWebSocketClient(POLYGON_API_KEY)
#     client.start()











import websocket
import json
import time

# Polygon.io API Configuration
POLYGON_API_KEY = "********************************"
POLYGON_WS_URL = "wss://delayed.polygon.io/stocks"
# POLYGON_WS_URL = "wss://socket.polygon.io/stocks"

class PolygonWebSocketClient:
    def __init__(self, api_key):
        self.api_key = api_key
        self.ws = None
        
    def on_message(self, ws, message):
        try:
            data = json.loads(message)
            print(f"\n🔔 Raw Message: {json.dumps(data, indent=2)}")
            
            # Check all message types
            for msg in data:
                print(f"Message type: {msg.get('ev')}")
                if msg.get('ev') == 'status':
                    print(f"Status: {msg.get('message')}")
                elif msg.get('ev') in ['A', 'AM']:
                    print(f"Stock data: {msg}")
                    
        except Exception as e:
            print(f"❌ Error: {e}")
            print(f"Raw message: {message}")

    def on_error(self, ws, error):
        print(f"❌ Error: {error}")

    def on_close(self, ws, close_status_code, close_msg):
        print(f"🔒 Connection closed: {close_status_code} - {close_msg}")

    def on_open(self, ws):
        print("✅ Connected to Polygon WebSocket")
        
        # Step 1: Authenticate
        auth_data = {"action": "auth", "params": self.api_key}
        ws.send(json.dumps(auth_data))
        print("🔐 Authentication sent")
        
        time.sleep(2)
        
        # Step 2: Subscribe to symbols
        subscriptions = ["AM.AAPL", "AM.MSFT"]
        
        for sub in subscriptions:
            sub_data = {"action": "subscribe", "params": 'AM.AAPL'}
            ws.send(json.dumps(sub_data))
            print(f"📡 Subscribed to: {sub}")
            time.sleep(1)

    def start(self):
        print("📶 Connecting to Polygon WebSocket...")
        self.ws = websocket.WebSocketApp(
            POLYGON_WS_URL,
            on_open=self.on_open,
            on_message=self.on_message,
            # on_error=self.on_error,
            # on_close=self.on_close
        )
        self.ws.run_forever()

if __name__ == "__main__":
    client = PolygonWebSocketClient(POLYGON_API_KEY)
    client.start()








